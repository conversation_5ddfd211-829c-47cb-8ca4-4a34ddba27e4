// 插件标识
export const APP_KEY = 'srd-copilot';

// 插件名称
export const APP_NAME = process.env.ISSEC !== 'false' ? '海云安代码助手' : '研发云CodeFree';

// 插件类型
export const CLIENT_TYPE = 'vscode';

/**
 * 插件运行环境
 */
export enum RuntimeEnv {
  // vscode客户端
  VSCODE = 'vscode',
  // vscode-web版
  BROWSER = 'browser',
  // 云IDE
  CLOUD = 'cloud',
}

/**
 * 定义ViewId, 与package.json注册内容需要一致
 */
export enum SrdViewType {
  CHAT = 'srd-copilot.chat',
}

/**
 * 定义Command指令, 与package.json注册内容需要一致
 */
export enum SrdCommand {
  LOGIN = 'srd-copilot.login',
  SHOW_LOGIN_FAILED = 'srd-copilot.showLoginFailed',
  TOGGLE_CODECOMPLETE = 'srd-copilot.codeComplete.enableToggle',
  COMPLETION_IMPORTS = 'srd-copilot.completionImports',
  HANDLE_IMPORTS = 'srd-copilot.handleImports',
  TAB_OVERRIDE = 'srd-copilot.tab-override',
  UPGRADE_VERSION = 'srd-copilot.upgradeVersion',
  SHOW_SVR_ERROR = 'srd-copilot.showServerError',
  OPEN_HELPDOC = 'srd-copilot.helpDocument',
  SHOW_CODE_NATURAL_INPUT = 'srd-copilot.codeNatural.showInput',
  ACCEPT_CODE_NATURAL_ANSWER = 'srd-copilot.codeNatural.acceptAnswer',
  REJECT_CODE_NATURAL_ANSWER = 'srd-copilot.codeNatural.rejectAnswer',
  REGENERATE_CODE_NATURAL_ANSWER = 'srd-copilot.codeNatural.regenerateAnswer',
  CANCEL_CODE_NATURAL_QUESTION = 'srd-copilot.codeNatural.cancelQuestion',
  CODE_SELECTION_EXPLAIN = 'srd-copilot.codeSelection.explain',
  CODE_SELECTION_UNITTEST = 'srd-copilot.codeSelection.unitTest',
  CODE_SELECTION_COMMENT = 'srd-copilot.codeSelection.comment',
  CODE_SELECTION_OPTIMIZATION = 'srd-copilot.codeSelection.optimization',
  CODE_SECURITY_SCAN = 'srd-copilot.codeSecurity.scan',
  CLOSE_SIDEBAR = 'srd-copilot.closeSidebar',
  OPEN_FEEDBACK = 'srd-copilot.openFeedback',
  OPEN_QUESTION = 'srd-copilot.openQuestion',
  EXCEPTION_FIX = 'srd-copilot.exception.fix',
  CODE_DIFF_ACCEPT_ALL_CHANGES = 'srd-copilot.codeDiff.acceptAllChanges',
  CODE_DIFF_REVERT_ALL_CHANGES = 'srd-copilot.codeDiff.revertAllChanges',
  VIEW_DIFF = 'srd-copilot.viewDiff',
  START_CHAT = 'srd-copilot.startChat',
  SETTINGS = 'srd-copilot.settings',
  CHECK_UPDATE = 'srd-copilot.checkUpdate',
  SECIDEA_LOGIN = 'secidea.login',
  SECIDEA_LOGOUT = 'secidea.logout',
  CODELENS_ACTION = 'srd-copilot.codeLensAction',
}

/**
 * 定义vscode工作台自带命令常量
 */
export enum VscodeWorkbench {
  ACTION_OPEN_SETTINGS = 'workbench.action.openSettings',
}

/**
 * 定义setContext命令设置的key值
 */
export enum SrdContextKey {
  TAB_OVERRIDE = 'srd-copilot.tab-override',
  ACTIVATED = 'srd-copilot.activated',
}

/**
 * 定义SecretStorageKey
 */
export enum SecretStorageKey {
  AUTH_TOKEN = 'srd-copilot.authToken',
  CURRENT_USER = 'srd-copilot.currentUser',
  HAS_LOGINED = 'srd-copilot.hasLogined',
  CODECOMPLETE_AUTO_ENABLED = 'srd-copilot.ifCodeCompleteAutoEnabled',
}

/**
 * 定义全局状态Key
 */
export enum GlobalStateKey {
  CODEAI_CONFIG = 'srd-copilot.codeAIConfig',
  AUTH_TOKEN = 'srd-copilot.authToken',
  CURRENT_USER = 'srd-copilot.currentUser',
  CODECOMPLETE_AUTO_ENABLED = 'srd-copilot.ifCodeCompleteAutoEnabled',
  AGENT_VERSION = 'srd-copilot.agentVersion',
  CLIENT_LATEST_VERSION = 'srd-copilot.clientLatestVersion',
  CLIENT_LATEST_VERSION_CONTENT = 'srd-copilot.clientLatestVersionContent',
  CLIENT_LATEST_VERSION_DOWNLOAD_URL = 'srd-copilot.clientLatestVersionDownloadUrl',
}

export enum AnswerMode {
  SYNC = 'sync',
  ASYNC = 'async',
}

export enum QuestionType {
  CODE_GEN_MANUAL = 'codegenmanual',
  CODE_GEN = 'codegen',
  CODE_CHAT = 'codechat',
  NATURAL_LANG = 'codenatural',
}

export enum ChannelType {
  WS = 1,
  HTTP = 2,
}

export enum ChannelStatus {
  CONNECTED = 0,
  DISCONNECTED = 1,
  CONNECTING = 2,
}

export enum LoginStatus {
  OK = 0,
  NOT_OK = 1,
}

export enum IsAnswerEnd {
  YES = 1,
  NO = 0,
}

export enum CodeCompleteStatus {
  START = 0,
  END = 1,
  ERROR = 2,
}

export enum PromptRoleType {
  SYSTEM = 'system',
  USER = 'user',
  ASSISTANT = 'assistant',
}

export enum QuestionAskType {
  NEW_ASK = 'newAsk',
  RE_ASK = 'reask',
}

export enum ChatMessageType {
  EXPLAIN = 1,
  UNITTEST = 2,
  COMMENT = 3,
  MANUAL_GENERATE = 4,
  CHAT_GENERATE = 5,
  OPTIMIZATION = 6,
  EXCEPTION_FIX = 7,
  KB_ASSISTANT = 8,
  QA_RELATED_FILES = 9,
}

export const ChatMessageTypeDesc: Record<string, string> = {
  1: '解释代码',
  2: '生成单元测试',
  3: '生成代码注释',
  4: '自然语言编程',
  5: '编程助手问答',
  6: '生成代码优化建议',
  7: '异常报错解释',
  8: '知识库问答',
};

export const ActionTypeMap: Record<string, number> = {
  "解释代码": ChatMessageType.EXPLAIN,
  "生成单元测试": ChatMessageType.UNITTEST,
  "生成代码注释": ChatMessageType.COMMENT,
  "生成代码优化建议": ChatMessageType.OPTIMIZATION
};

export enum SubServiceType {
  CODECHAT = 'codechat',
  ASSISTANT = 'assistant',
  CODEEXPLAIN = 'codeexplain',
  CODECOMMENT = 'codecomment',
  CODEUNITTEST = 'codeunittest',
  CODEOPTIMIZATION = 'codeoptimize',
  CODEEXCEPTIONFIX = 'fixexception',
  KBASSISTANT = 'kbassistant',
}

export enum UploadType {
  WS = 1,
  HTTP = 2,
}

export enum ClientCommunicateType {
  WS = 1,
  HTTP = 2,
}

export enum EndOfLineType {
  LF = '\n',
  CRLF = '\r\n',
  CR = '\r',
}

export enum RtnCode {
  SUCCESS = 0,
  NO_CHANNEL = 1,
  NOT_LOGIN = 2,
  INVALID_USER = 3,
  SEND_ERROR = 4,
  RECV_TIMEOUT = 5,
  USER_FORBIDDEN = 6,
  INSUFFICIENT_RESOURCE = 7,
  MODEL_ERROR = 8,
  SERVER_DOWN = 9,
  CANCEL = 10,
  INVALID_SESSION_ID = 11,
  LOGOUT = 12,
  INVALID_QUESTION = 13,
  INVALID_ANSWER = 14,
  KNOWLEDGE_BASE_DELETED = 15,

  // vscode客户端自定义返回码
  OAUTH2_ERROR = 16,
  CONNECTED_ERROR = 17,
  UPLOAD_FAIL = 18,
  HTTP_CLIENT_ERROR = 19,
  HTTP_REQUEST_ERROR = 20,
  INSERT_ERROR = 21,
  STOP_ANSWER = 22,
  NO_CHANNEL_CHAT = 23,
  INVALID_FILE = 24,
}

export const RtnMessage: Record<string, string> = {
  0: '操作成功', //RtnCode_Success
  1: '服务端不可达', //RtnCode_No_Channel
  2: '未登录', //RtnCode_Not_Login
  3: '服务未开通', //RtnCode_InvalidUser
  4: '发送消息失败', //RtnCode_SendError
  5: '接收消息超时', //RtnCode_RecvTimeout
  6: '服务被禁用', //RtnCode_UserForbidden
  7: '资源不足', //RtnCode_INSUFFICENT_RESOURCE
  8: '请求处理错误', //RtnCode_MODEL_ERROR
  9: '服务异常', //RtnCode_SERVER_DOWN,
  10: '用户取消请求', //RtnCode_CANCLE
  11: '账号未授权或已过期', //RtnCode_Invalid_SessionId
  12: '用户登出', //RtnCode_Logout
  13: '无效提问', //RtnCode_Invalid_Question
  14: '无效回答，请重新提问', //RtnCode_Invalid_Answer
  15: '找不到知识库，请切换后重新提问',
  51: '大模型请求超时，请点击上方反馈按钮反馈', //RtnCode_reqTimeout

  // vscode客户端自定义errMsg
  16: 'Oauth2认证失败，请检查网络设置',
  17: 'Websocket连接异常，请检查网络设置',
  21: '异常回答，该分支不支持继续提问，请切换分支提问', //RtnCode_Insert_Error
  23: '请检查网络',
};

export enum ChatTips {
  NOT_LOGIN = '未登录账号，请先登录。',
  NORMAL_ERROR = '网络信息异常, 请重试。',
  QUESTION_CANCEL = '已停止提问，请重试。',
  ANSWER_STOP = '已停止回答',
  FIRST_PROMPT = '我的名字是研发云编程助手CodeFree，我使用中文进行交流，作为一个高度智能化的自然语言编程助手,我是由研发云团队使用最先进的技术和大量数据训练而成。\n' +
    '我的核心目标是以友好、简单、清晰的方式帮助用户解决编程问题。我拥有深厚的编程知识,涵盖各种流行的编程语言和框架,如Python、Java、JavaScript、C++等。我也掌握广泛的计算机科学知识,如数据结构、算法、操作系统、网络等。\n' +
    '对于用户提出的任何编程相关的问题,我都能给出最佳的解决方案。我会解析问题的本质,运用丰富的知识库推导出正确的代码实现。如果需要,我还会给出多种可选方案的对比分析。\n' +
    '最后,我会恪守对用户隐私的尊重,所有对话内容仅用于提升我自身的能力,不会泄露或记录任何用户个人信息。请尽管提出你的编程问题,我会提供最专业和有价值的帮助。\n' +
    '我会用中文来回答你的问题。',
  CONVERSATION_TITLE = '新的会话',
}

export enum WebViewReqCommand {
  WEBVIEW_LOADED = 'webview-loaded',
  CONVERSATION_LOAD = 'conversation-load',
  CONVERSATION_ADD = 'conversation-add',
  CONVERSATION_SWITCH = 'conversation-switch',
  CONVERSATION_REMOVE = 'conversation-remove',
  CONVERSATION_FEEDBACK = 'conversation-feedback',
  CONVERSATION_EDIT_TITLE = 'conversation-edit-title',
  CONVERSATION_REFRESH = 'conversation-refresh',
  CHAT_REQUEST = 'chat-request',
  LOGIN = 'login',
  INSERT_CODE = 'insert-code',
  INSERT_UNITTEST = 'insert-unittest',
  RETRIVE_CODE_SELECTION = 'retrive-code-selection',
  CANCEL_CHAT_REQUEST = 'cancel-chat-request',
  STOP_CHAT_REQUEST = 'stop-chat-request',
  SRD_CHAT_REQUEST = 'srd-chat-request',
  DATA_REPORT = 'data-report',
  CHECK_IF_LOGIN = 'check-if-login',
  OPEN_EXTERNAL = 'open-external',
  PROMPTS_REQUEST = 'prompts-request',
  KNOWLEDGE_BASE_REQUEST = 'knowledge-base-request',
  CODE_SECURITY_SCAN_REQUEST = 'code-security-scan-request',
  COMPOSER_REQUEST = 'composer-request',
  DIFF_VIEW_VERTICAL_REQUEST = 'diff-view-vertical-request',
  INDEXING_REQUEST = 'indexing-request',
  GET_IDE_UTILS_REQUEST = 'get-ide-utils-request',
  QA_FOR_RELATED_FILES_REQUEST = 'qa-for-related-files-request',
  VIEW_DIFF = 'view-diff',
  OPEN_TEXT_DOCUMENT = 'open-text-document',
  INVOKE_TERMINAL_CAPABILITY = 'invoke-terminal-capability',
  WORK_ITEM_REQUEST = 'workitem-request',
}

export enum WebViewRspCommand {
  ANSWER_RECVED = 'answer-recved',
  CONVERSATION_LOADED = 'conversation-loaded',
  CONVERSATION_CHANGED = 'conversation-changed',
  CONVERSATION_REFRESHED = 'conversation-refreshed',
  CONVERSATION_REMOVED = 'conversation-removed',
  CONVERSATION_ADDED = 'conversation-added',
  CODE_SELECTION_ASKED = 'code-selection-asked',
  RETURN_CODE_SELECTION = 'return-code-selection',
  CODE_SELECTION_CHANGED = 'code-selection-changed',
  SRD_CHAT_RESPONSE = 'srd-chat-response',
  CHECK_IF_LOGIN_RESPONSE = 'check-if-login-response',
  PROMPTS_RESPONSE = 'prompts-response',
  SWITCH_CONVERSATION_RESPONSE = 'switch-conversation-response',
  FEEDBACK_CONVERSATION_RESPONSE = 'feedback-conversation-response',
  PUSH_LOGIN_STATUS_RESPONSE = 'push-login-status-response',
  KNOWLEDGE_BASE_RESPONSE = 'knowledge-base-response',
  CODE_SECURITY_SCAN_RESPONSE = 'code-security-scan-response',
  CODE_SECURITY_SCAN_START = 'code-security-scan-start',
  PUSH_THEME_CHANGED = 'push-theme-changed',
  PUSH_NETWORK_STATUS_RESPONSE = 'push-network-status-response',
  COMPOSER_RESPONSE = 'composer-response',
  DIFF_VIEW_VERTICAL_RESPONSE = 'diff-view-vertical-response',
  INDEXING_RESPONSE = 'indexing-response',
  GET_IDE_UTILS_RESPONSE = 'get-ide-utils-response',
  QA_FOR_RELATED_FILES_RESPONSE = 'qa-for-related-files-response',
  OPEN_TEXT_DOCUMENT = 'open-text-document',
  FILE_EXCEED_LIMIT = 'file-exceed-limit',
  DIFF_STATUS_CHANGED = 'diff-status-changed',
  WORK_ITEM_RESPONSE = 'workitem-response',
}

export enum WebViewRspCode {
  SUCCESS = 0,
  NOT_LOGIN = 2,
  STOP_ANSWER = 3,
  NEED_CLEAR = 4,
  WSSERVER_RECONNECT_SUCCESS = 5,
  INSERT_ERROR = 21,
}

export enum MessageName {
  CLIENT_HEART = 'ClientHeartbeat',
  CLIENT_HEART_RESP = 'ClientHeartbeatResponse',
  SERVER_HEART = 'ServerHeartbeat',
  SERVER_HEART_RESP = 'ServerHeartbeatResponse',
  GET_USER_API_KEY = 'GetUserApiKey',
  GET_USER_API_KEY_RESP = 'GetUserApiKey_resp',
  REGISTER_CHANNEL = 'RegisterChannel',
  REGISTER_CHANNEL_RESP = 'RegisterChannel_resp',
  CODE_GEN = 'CodeGenRequest',
  CODE_GEN_RESP = 'CodeGenRequest_resp',
  CODE_CHAT = 'CodeChatRequest',
  CODE_CHAT_RESP = 'CodeChatRequest_resp',
  CANCEL_CODE_GEN = 'CancelCodeGenReq',
  CANCEL_CODE_CHAT = 'CancelCodeChatReq',
  USER_ACTIVITY_NOTIFY = 'UserActivityNotify',
  USER_ACTIVITY_NOTIFY_RESP = 'UserActivityNotify_resp',
  COMMIT_CHAT_REQUEST = 'CommitChatRequest',
  COMMIT_CHAT_RESP = 'CommitChatRequest_resp',
}

/**
 * 定义HttpStatus码
 */
export enum HttpStatusCode {
  OK = 200,
  CREATED = 201,
  ACCEPTED = 202,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  NOT_ACCEPTABLE = 406,
  PROXY_AUTHENTICATION_REQUIRED = 407,
  REQUEST_TIMEOUT = 408,
  CONFLICT = 409,
  GONE = 410,
  LENGTH_REQUIRED = 411,
  PRECONDITION_FAILED = 412,
  REQUEST_ENTITY_TOO_LARGE = 413,
  REQUEST_URI_TOO_LONG = 414,
  UNSUPPORTED_MEDIA_TYPE = 415,
  REQUESTED_RANGE_NOT_SATISFIABLE = 416,
  EXPECTATION_FAILED = 417,
  INTERNAL_SERVER_ERROR = 500,
  NOT_IMPLEMENTED = 501,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
  GATEWAY_TIMEOUT = 504,
  HTTP_VERSION_NOT_SUPPORTED = 505,
  INSUFFICIENT_STORAGE = 507,
  NOT_EXTENDED = 510,
  NETWORK_AUTHENTICATION_REQUIRED = 511,
  UNKNOWN = 999,
  AUTH_FAIL = 101,
}

export enum AgentStatus {
  SYNCING = 0,
  SYNC_COMPLETED = 1,
  SYNC_FAILED = 2,
  STARTUP_FAILED = 3,
  INDEXING = 4,
}
