import * as vscode from 'vscode';
import { SrdCommand, VscodeWorkbench } from '../common/constants';
import { SECIDEA_SETTINGS_ITEM } from '../common/config';
import { LanguageConfig } from './type';
import {
  clearClientPropertiesEndpoint,
  handleDisableLanguageChange,
  updateClientPropertiesCplMode,
  updateClientPropertiesEndpoint,
  updateClientPropertiesKeybinding,
  updateClientPropertiesTriggerMode,
} from './utils';
import { GENERAL_ADDRESS_SETTING } from './constants';
import SettingsWatcher from './settingsWatcher';
import { getAddress } from './settingsGetter';
import {
  testConnect,
  userInfoManager,
  LoginStatus as SecideaLoginStatus,
} from '../login/loginUtils';
import { srdAuthProviderInstance, tryLoginBySrdcloud } from '../authentication/index';

export async function registerSettings(context: vscode.ExtensionContext) {
  // 仅当品牌为Secidea时，才有设置页面
  if (process.env.ISSEC !== 'false') {
    // 不做证书校验
    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

    // 注册配置页面的打开
    context.subscriptions.push(
      vscode.commands.registerCommand(SrdCommand.SETTINGS, () => {
        vscode.commands.executeCommand(VscodeWorkbench.ACTION_OPEN_SETTINGS, SECIDEA_SETTINGS_ITEM);
      })
    );

    // 注册登录命令
    context.subscriptions.push(
      vscode.commands.registerCommand(SrdCommand.SECIDEA_LOGIN, () => {
        if (userInfoManager.getLoginStatus() === SecideaLoginStatus.notLoggedIn) {
          tryLoginBySrdcloud(false);
        }
      }),
      vscode.commands.registerCommand(SrdCommand.SECIDEA_LOGOUT, () => {
        srdAuthProviderInstance?.removeSession();
      })
    );

    // 注册打开快捷键设置命令
    context.subscriptions.push(
      vscode.commands.registerCommand('secidea.openKeybindings', () => {
        vscode.commands.executeCommand('workbench.action.openGlobalKeybindings', '自定义');
      })
    );

    // 配置项监听
    const settingsWatcher = new SettingsWatcher(context);
    settingsWatcher.watch();

    // 测试服务器连接情况， 如果不通则跳转到设置页面
    if (!(await testConnect())) {
      if (getAddress().trim() === '') {
        // 初次设置
        const selection = await vscode.window.showInformationMessage(
          '检测到未设置服务器地址，是否立即进行设置',
          '是',
          '否'
        );
        if (selection === '是') {
          vscode.commands.executeCommand(
            VscodeWorkbench.ACTION_OPEN_SETTINGS,
            SECIDEA_SETTINGS_ITEM
          );
        }
      } else {
        // 有服务器地址，但是网络不通
        const selection = await vscode.window.showWarningMessage(
          '当前服务器网络连接不通, 是否立即检查服务器地址',
          '是',
          '否'
        );
        if (selection === '是') {
          vscode.commands.executeCommand(
            VscodeWorkbench.ACTION_OPEN_SETTINGS,
            SECIDEA_SETTINGS_ITEM
          );
        }
      }
    }
  }

  // 初始化设置页面的监听器
  initCplConfigurationListeners();
}

function initCplConfigurationListeners(): void {
  vscode.workspace.onDidChangeConfiguration(async event => {
    if (event.affectsConfiguration('代码助手.禁用补全语言')) {
      const disableLanguageList = vscode.workspace
        .getConfiguration('代码助手')
        .get<LanguageConfig[]>('禁用补全语言');
      if (!disableLanguageList) {
        throw new Error('禁用补全语言配置无效');
      }
      await handleDisableLanguageChange(disableLanguageList);
    }

    if (event.affectsConfiguration(GENERAL_ADDRESS_SETTING)) {
      const endpoint = getAddress();
      if (endpoint && endpoint.trim().length > 0) {
        updateClientPropertiesEndpoint(endpoint);
        // nodecli!.stdin.write(JSON.stringify(updateConfigRequest) + "\n");
      } else {
        clearClientPropertiesEndpoint();
        // nodecli!.stdin.write(JSON.stringify(clearConfigRequest) + "\n");
      }
    }

    if (event.affectsConfiguration('代码助手.补全模式')) {
      const cplMode = vscode.workspace.getConfiguration('代码助手').get<string>('补全模式');
      // console.debug(`Completion mode changed to: ${cplMode}`);
      await updateClientPropertiesCplMode(cplMode!);
    }

    if (event.affectsConfiguration('代码助手.补全方式')) {
      const triggerMode =
        vscode.workspace.getConfiguration('代码助手').get<string>('补全方式') === '自动'
          ? 'automatic'
          : 'manual';
      // console.debug(`Trigger mode changed to: ${triggerMode}`);
      await updateClientPropertiesTriggerMode(triggerMode);
    }

    if (event.affectsConfiguration('代码助手.快捷键配置')) {
      const keybindings =
        vscode.workspace.getConfiguration('代码助手').get<string>('快捷键配置') === '默认'
          ? 'vscode-style'
          : '';
      await updateClientPropertiesKeybinding(keybindings);
      if (keybindings === '') {
        vscode.commands.executeCommand('secidea.openKeybindings');
      }
    }
  });
}
