import * as vscode from 'vscode';
import SrdStatusBar from './srdStatusBar';
import { RtnCode, RtnMessage, SrdCommand } from '../common/constants';
import { LoginServiceInst } from '../service/loginService';
import { FORBIDDEN_PATH, NO_SUBSCRIBE_PATH, HELPDOC_PATH } from '../common/config';
import AutoCompleteStatusStore from './autoCompleteStatusStore';
import { getHttpServerHost } from '../utils/envUtil';

/**
 * 注册状态栏
 * @param context context
 */
export function registerSrdStatusBar(context: vscode.ExtensionContext) {
  // 初始自动补全状态
  AutoCompleteStatusStore.initEnabled();

  // 初始化状态栏
  const srdStatusBar = new SrdStatusBar(context);
  srdStatusBar.setObservable(LoginServiceInst);
  srdStatusBar.init();

  /**
   * 注册状态栏
   */
  context.subscriptions.push(srdStatusBar);

  /**
   * 注册服务异常展示
   */
  context.subscriptions.push(
    vscode.commands.registerCommand(SrdCommand.SHOW_SVR_ERROR, async (code: RtnCode) => {
      const selection = await vscode.window.showInformationMessage(
        RtnMessage[code],
        '详情',
        '取消'
      );

      if (selection === '详情') {
        const path = code === RtnCode.INVALID_USER ? NO_SUBSCRIBE_PATH : FORBIDDEN_PATH;
        const uri = `${getHttpServerHost()}${path}`;
        vscode.env.openExternal(vscode.Uri.parse(uri));
      }
    })
  );

  /**
   * 注册启用补全命令
   */
  context.subscriptions.push(
    vscode.commands.registerCommand(SrdCommand.TOGGLE_CODECOMPLETE, async () => {
      const enabled = await AutoCompleteStatusStore.checkIfEnabled();

      if (enabled) {
        await AutoCompleteStatusStore.setEnabled(false);
        srdStatusBar.setIfEnabled(false);
      } else {
        await AutoCompleteStatusStore.setEnabled(true);
        srdStatusBar.setIfEnabled(true);
      }
    })
  );

  /**
   * 注册打开帮助文档命令
   */
  context.subscriptions.push(
    vscode.commands.registerCommand(SrdCommand.OPEN_HELPDOC, () => {
      const uri = `${getHttpServerHost()}${HELPDOC_PATH}`;
      vscode.env.openExternal(vscode.Uri.parse(uri));
    })
  );

  return srdStatusBar;
}
