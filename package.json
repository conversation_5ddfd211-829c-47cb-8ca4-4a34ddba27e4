{"name": "Secidea", "displayName": "海云安代码助手", "publisher": "Secidea", "description": "海云安代码助手插件是一款高效、可靠的缺陷扫描展示工具，与海云安源代码检测和开源组件检测平台无缝对接", "version": "3.5.0-c10", "private": true, "engines": {"vscode": "^1.68.1"}, "icon": "assets/logo.png", "license": "SEE LICENSE IN LICENSE", "categories": ["Programming Languages", "Machine Learning", "Education", "Snippets"], "keywords": ["ai", "openai", "codex", "pilot", "snippets", "documentation", "autocomplete", "intellisense", "refactor", "javascript", "python", "typescript", "php", "go", "golang", "ruby", "c++", "c#", "java", "kotlin", "co-pilot"], "homepage": "https://secidea.com/Secidea/secidea", "repository": {"type": "git", "url": "https://secidea.com/Secidea/secidea"}, "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "enabledApiProposals": ["interactive", "interactiveUserActions"], "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "contributes": {"commands": [{"command": "srd-copilot.settings", "title": "设置", "category": "海云安代码助手", "icon": "$(gear)"}, {"command": "srd-copilot.startChat", "title": "开始聊天", "category": "海云安代码助手"}, {"command": "srd-copilot.helpDocument", "title": "帮助文档", "category": "海云安代码助手"}, {"command": "srd-copilot.login", "title": "CodeFree登录", "category": "海云安代码助手"}, {"command": "srd-copilot.codeComplete.enableToggle", "title": "代码补全启用/禁用", "category": "海云安代码助手"}, {"command": "srd-copilot.codeSelection.explain", "title": "解释代码", "category": "海云安代码助手"}, {"command": "srd-copilot.codeSelection.unitTest", "title": "生成单元测试", "category": "海云安代码助手"}, {"command": "srd-copilot.codeSelection.comment", "title": "生成代码注释", "category": "海云安代码助手"}, {"command": "srd-copilot.codeSelection.optimization", "title": "生成代码优化建议", "category": "海云安代码助手"}, {"command": "srd-copilot.openQuestion", "title": "帮助", "icon": {"light": "assets/toolbar/question-light-draw.svg", "dark": "assets/toolbar/question-dark-draw.svg"}}, {"command": "srd-copilot.openFeedback", "title": "反馈", "icon": {"light": "assets/toolbar/conversation-light-draw.svg", "dark": "assets/toolbar/conversation-dark-draw.svg"}}, {"command": "srd-copilot.closeSidebar", "title": "关闭", "icon": {"light": "assets/toolbar/close-draw-light.svg", "dark": "assets/toolbar/close-draw-dark.svg"}}, {"command": "srd-copilot.codeDiff.acceptAllChanges", "title": "Accept All Changes", "icon": "$(check-all)"}, {"command": "srd-copilot.codeDiff.revertAllChanges", "title": "Revert All <PERSON>", "icon": "$(discard)"}, {"command": "srd-copilot.aiScmCommand", "title": "生成提交信息并关联工作项", "icon": "assets/codefree.svg"}, {"command": "srd-copilot.stopCommitAnalysis", "title": "停止提交分析", "category": "研发云CodeFree", "icon": {"light": "assets/toolbar/stopAiCommit-light.svg", "dark": "assets/toolbar/stopAiCommit-dark.svg"}}, {"command": "srd-copilot.codeLensAction", "title": "代码透镜"}], "keybindings": [{"command": "editor.action.inlineSuggest.trigger", "key": "ctrl+enter", "mac": "cmd+enter", "when": "editorTextFocus && !editorHasSelection && !inlineSuggestionsVisible"}, {"command": "editor.action.inlineSuggest.showPrevious", "key": "alt+[", "mac": "alt+[", "when": "inlineSuggestionsVisible"}, {"command": "editor.action.inlineSuggest.showNext", "key": "alt+]", "mac": "alt+]", "when": "inlineSuggestionsVisible"}, {"command": "editor.action.inlineSuggest.commit", "key": "tab", "mac": "tab", "when": "inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus"}, {"command": "srd-copilot.codeComplete.enableToggle", "key": "ctrl+alt+shift+o", "mac": "cmd+alt+shift+o", "when": "srd-copilot.activated"}, {"command": "srd-copilot.chat.focus", "key": "alt+shift+k", "mac": "alt+shift+k"}, {"command": "srd-copilot.startChat", "key": "alt+shift+k", "mac": "alt+shift+k"}], "menus": {"view/title": [{"command": "srd-copilot.openQuestion", "group": "navigation@0", "when": "view == srd-copilot.chat"}, {"command": "srd-copilot.openFeedback", "group": "navigation@1", "when": "view == srd-copilot.chat"}, {"command": "srd-copilot.closeSidebar", "group": "navigation@2", "when": "view == srd-copilot.chat"}], "editor/context": [{"submenu": "srd-copilot/editor/context/menuItems", "group": "navigation"}], "srd-copilot/editor/context/menuItems": [{"command": "srd-copilot.helpDocument", "group": "srd-copilot-menu-group@1"}, {"command": "srd-copilot.login", "group": "srd-copilot-menu-group@2", "when": "!srd-copilot.activated"}, {"command": "srd-copilot.startChat", "group": "srd-copilot-menu-group@3"}, {"command": "srd-copilot.codeComplete.enableToggle", "group": "srd-copilot-menu-group@4", "when": "srd-copilot.activated"}, {"command": "srd-copilot.codeSelection.explain", "group": "srd-copilot-menu-group@5"}, {"command": "srd-copilot.codeSelection.unitTest", "group": "srd-copilot-menu-group@6"}, {"command": "srd-copilot.codeSelection.comment", "group": "srd-copilot-menu-group@7"}, {"command": "srd-copilot.codeSelection.optimization", "group": "srd-copilot-menu-group@8"}], "commandPalette": [], "editor/title": [{"when": "isInDiffEditor", "command": "srd-copilot.codeDiff.acceptAllChanges", "group": "navigation@1"}, {"when": "isInDiffEditor", "command": "srd-copilot.codeDiff.revertAllChanges", "group": "navigation@2"}]}, "submenus": [{"id": "srd-copilot/editor/context/menuItems", "label": "海云安代码助手"}], "viewsContainers": {"activitybar": [{"id": "srd-chat", "title": "海云安代码助手", "icon": "assets/chat.svg"}]}, "views": {"srd-chat": [{"id": "srd-copilot.chat", "type": "webview", "name": "海云安代码助手"}]}, "icons": {"srd-copilot-unlogin": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e791"}}, "srd-copilot-error-info": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e7a0"}}, "srd-copilot-unconnect": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e78d"}}, "srd-copilot-code-enabled": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e78e"}}, "srd-copilot-code-disabled": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e78f"}}, "srd-copilot-select": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e7b4"}}, "srd-copilot-delete": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e763"}}, "srd-copilot-refresh": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e7f7"}}, "srd-copilot-logo": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e7b3"}}, "srd-copilot-cancel": {"description": "SRD Copilot icon", "default": {"fontPath": "assets/iconfont.woff", "fontCharacter": "\\e65f"}}}, "iconFonts": [{"id": "srd-copilot-font", "src": [{"path": "assets/iconfont.woff", "format": "woff"}]}], "configuration": [{"title": "海云安代码助手"}, {"title": "基础配置", "properties": {"通用.服务器扫描地址": {"type": "string", "order": 0, "description": "协议://IP地址:端口/项目名,如 http://***********:3000/oscap", "pattern": "^https?://.+"}, "通用.登录状态": {"type": "string", "order": 1, "default": "未登录", "readOnly": true, "markdownDescription": "[**点此登录**](command:secidea.login)  [**点此登出**](command:secidea.logout)  \n  请先配置正确服务器扫描地址，登录成功后，下方会显示用户信息"}, "通用.版本信息": {"type": "object", "order": 2, "markdownDescription": "插件版本号：3.5.0-c10 [检查更新](command:srd-copilot.checkUpdate)", "default": {"新版本插件安装包存放目录": "", "是否自动下载新版本": false, "是否开启更新提醒": false, "更新提醒间隔小时": 1}, "properties": {"新版本插件安装包存放目录": {"type": "string"}, "是否自动下载新版本": {"type": "boolean"}, "是否开启更新提醒": {"type": "boolean"}, "更新提醒间隔小时": {"type": "number"}}, "additionalProperties": false}}}, {"title": "代码助手配置", "properties": {"代码助手.是否展示函数级别快捷键": {"type": "boolean", "default": true, "description": "是否展示函数级别快捷键"}, "代码助手.禁用补全语言": {"type": "array", "description": "禁用补全的语言设置", "items": {"type": "object", "properties": {"语言": {"type": "string", "description": "语言"}, "尾缀名": {"type": "string", "description": "尾缀名"}, "是否禁用": {"type": "boolean", "description": "是否禁用"}}, "required": ["语言", "尾缀名", "是否禁用"]}, "default": [{"语言": "ABAP", "尾缀名": "abap", "是否禁用": false}, {"语言": "ActionScript", "尾缀名": "as", "是否禁用": false}, {"语言": "Ada", "尾缀名": "ada", "是否禁用": false}, {"语言": "AsciiDoc", "尾缀名": "adoc", "是否禁用": false}, {"语言": "<PERSON><PERSON><PERSON>", "尾缀名": "am", "是否禁用": false}, {"语言": "AppleScript", "尾缀名": "applescript", "是否禁用": false}, {"语言": "Arc", "尾缀名": "arc", "是否禁用": false}, {"语言": "ASP", "尾缀名": "asp", "是否禁用": false}, {"语言": "Assembly", "尾缀名": "asm", "是否禁用": false}, {"语言": "AutoHotkey", "尾缀名": "ahk", "是否禁用": false}, {"语言": "AutoIt", "尾缀名": "au3", "是否禁用": false}, {"语言": "Awk", "尾缀名": "as", "是否禁用": false}, {"语言": "<PERSON><PERSON>", "尾缀名": "bat", "是否禁用": false}, {"语言": "<PERSON><PERSON>", "尾缀名": "bzl", "是否禁用": false}, {"语言": "BibTeX", "尾缀名": "bib", "是否禁用": false}, {"语言": "<PERSON>ison", "尾缀名": "bison", "是否禁用": false}, {"语言": "BitBake", "尾缀名": "bb", "是否禁用": false}, {"语言": "Blade", "尾缀名": "blade.php", "是否禁用": false}, {"语言": "<PERSON><PERSON>", "尾缀名": "sh", "是否禁用": false}, {"语言": "C", "尾缀名": "c", "是否禁用": false}, {"语言": "C#", "尾缀名": "cs", "是否禁用": false}, {"语言": "C++", "尾缀名": "cpp", "是否禁用": false}, {"语言": "CMake", "尾缀名": "cmake", "是否禁用": false}, {"语言": "COBOL", "尾缀名": "cob", "是否禁用": false}, {"语言": "CoffeeScript", "尾缀名": "coffee", "是否禁用": false}, {"语言": "ColdFusion", "尾缀名": "cfm", "是否禁用": false}, {"语言": "Clojure", "尾缀名": "clj", "是否禁用": false}, {"语言": "CSS", "尾缀名": "css", "是否禁用": false}, {"语言": "CSV", "尾缀名": "csv", "是否禁用": false}, {"语言": "CUDA", "尾缀名": "cu", "是否禁用": false}, {"语言": "D", "尾缀名": "d", "是否禁用": false}, {"语言": "Dart", "尾缀名": "dart", "是否禁用": false}, {"语言": "Delphi", "尾缀名": "dpr", "是否禁用": false}, {"语言": "<PERSON>", "尾缀名": "pas", "是否禁用": false}, {"语言": "Diff", "尾缀名": "diff", "是否禁用": false}, {"语言": "Patch", "尾缀名": "patch", "是否禁用": false}, {"语言": "Dockerfile", "尾缀名": "dockerfile", "是否禁用": false}, {"语言": "DTD", "尾缀名": "dtd", "是否禁用": false}, {"语言": "Erl<PERSON>", "尾缀名": "erl", "是否禁用": false}, {"语言": "<PERSON><PERSON><PERSON>", "尾缀名": "ex", "是否禁用": false}, {"语言": "<PERSON><PERSON><PERSON>", "尾缀名": "exs", "是否禁用": false}, {"语言": "Elm", "尾缀名": "elm", "是否禁用": false}, {"语言": "EEx", "尾缀名": "eex", "是否禁用": false}, {"语言": "LiveEEx", "尾缀名": "leex", "是否禁用": false}, {"语言": "F#", "尾缀名": "fs", "是否禁用": false}, {"语言": "Fortran", "尾缀名": "f", "是否禁用": false}, {"语言": "Fortran90", "尾缀名": "f90", "是否禁用": false}, {"语言": "Fish", "尾缀名": "fish", "是否禁用": false}, {"语言": "Forth", "尾缀名": "fth", "是否禁用": false}, {"语言": "GLSL", "尾缀名": "glsl", "是否禁用": false}, {"语言": "Go", "尾缀名": "go", "是否禁用": false}, {"语言": "GraphQL", "尾缀名": "gql", "是否禁用": false}, {"语言": "Groovy", "尾缀名": "groovy", "是否禁用": false}, {"语言": "<PERSON><PERSON><PERSON>", "尾缀名": "gradle", "是否禁用": false}, {"语言": "<PERSON><PERSON>", "尾缀名": "haml", "是否禁用": false}, {"语言": "<PERSON><PERSON>", "尾缀名": "hs", "是否禁用": false}, {"语言": "HCL", "尾缀名": "hcl", "是否禁用": false}, {"语言": "HLSL", "尾缀名": "hlsl", "是否禁用": false}, {"语言": "HTML", "尾缀名": "html", "是否禁用": false}, {"语言": "HTML", "尾缀名": "htm", "是否禁用": false}, {"语言": "HTTP", "尾缀名": "http", "是否禁用": false}, {"语言": "Haxe", "尾缀名": "hx", "是否禁用": false}, {"语言": "INI", "尾缀名": "ini", "是否禁用": false}, {"语言": "Java", "尾缀名": "java", "是否禁用": false}, {"语言": "JavaScript", "尾缀名": "js", "是否禁用": false}, {"语言": "JSX", "尾缀名": "jsx", "是否禁用": false}, {"语言": "JSON", "尾缀名": "json", "是否禁用": false}, {"语言": "<PERSON>", "尾缀名": "jl", "是否禁用": false}, {"语言": "<PERSON><PERSON><PERSON>", "尾缀名": "kt", "是否禁用": false}, {"语言": "<PERSON><PERSON><PERSON>", "尾缀名": "kts", "是否禁用": false}, {"语言": "LaTeX", "尾缀名": "tex", "是否禁用": false}, {"语言": "Less", "尾缀名": "less", "是否禁用": false}, {"语言": "Lisp", "尾缀名": "lisp", "是否禁用": false}, {"语言": "<PERSON><PERSON>", "尾缀名": "lua", "是否禁用": false}, {"语言": "MATLAB/Objective-C", "尾缀名": "m", "是否禁用": false}, {"语言": "Objective-C++", "尾缀名": "mm", "是否禁用": false}, {"语言": "<PERSON><PERSON>", "尾缀名": "md", "是否禁用": false}, {"语言": "<PERSON><PERSON><PERSON>", "尾缀名": "makefile", "是否禁用": false}, {"语言": "<PERSON>", "尾缀名": "nix", "是否禁用": false}, {"语言": "OCaml", "尾缀名": "ml", "是否禁用": false}, {"语言": "<PERSON><PERSON>", "尾缀名": "pl", "是否禁用": false}, {"语言": "PHP", "尾缀名": "php", "是否禁用": false}, {"语言": "PowerShell", "尾缀名": "ps1", "是否禁用": false}, {"语言": "Protocol Buffers", "尾缀名": "proto", "是否禁用": false}, {"语言": "Python", "尾缀名": "py", "是否禁用": false}, {"语言": "R", "尾缀名": "r", "是否禁用": false}, {"语言": "<PERSON>", "尾缀名": "rb", "是否禁用": false}, {"语言": "Rust", "尾缀名": "rs", "是否禁用": false}, {"语言": "Sass", "尾缀名": "sass", "是否禁用": false}, {"语言": "Scala", "尾缀名": "scala", "是否禁用": false}, {"语言": "SCSS", "尾缀名": "scss", "是否禁用": false}, {"语言": "SQL", "尾缀名": "sql", "是否禁用": false}, {"语言": "<PERSON><PERSON><PERSON>", "尾缀名": "styl", "是否禁用": false}, {"语言": "SVG", "尾缀名": "svg", "是否禁用": false}, {"语言": "Swift", "尾缀名": "swift", "是否禁用": false}, {"语言": "Tcl", "尾缀名": "tcl", "是否禁用": false}, {"语言": "Terraform", "尾缀名": "tf", "是否禁用": false}, {"语言": "TypeScript", "尾缀名": "ts", "是否禁用": false}, {"语言": "TSX", "尾缀名": "tsx", "是否禁用": false}, {"语言": "Twig", "尾缀名": "twig", "是否禁用": false}, {"语言": "Text", "尾缀名": "txt", "是否禁用": false}, {"语言": "VB.NET", "尾缀名": "vb", "是否禁用": false}, {"语言": "<PERSON><PERSON>", "尾缀名": "vue", "是否禁用": false}, {"语言": "XML", "尾缀名": "xml", "是否禁用": false}, {"语言": "XSLT", "尾缀名": "xsl", "是否禁用": false}, {"语言": "YAML", "尾缀名": "yaml", "是否禁用": false}, {"语言": "YAML", "尾缀名": "yml", "是否禁用": false}, {"语言": "Zig", "尾缀名": "zig", "是否禁用": false}], "markdownDescription": "配置禁用代码补全功能的语言类型", "order": 2}, "代码助手.补全模式": {"type": ["string"], "enum": ["精准优先", "平衡模式", "速度优先"], "default": "精准优先", "markdownDescription": "选择使用代码助手进行补全的模式", "order": 1}, "代码助手.快捷键配置": {"type": ["string"], "enum": ["默认", "自定义"], "default": "默认", "enumDescriptions": ["`Ctrl + Enter` 手动发起补全请求\n`Tab` 选择当前补全建议\n`Alt + [` 或 `Alt + ]` 查看上一条/下一条补全建议\n`Ctrl + Alt + Shift + O` 启用/禁用代码补全", "自定义快捷键"], "markdownDescription": "配置代码助手补全对应的快捷键", "order": 3}, "代码助手.打开快捷键设置": {"type": "boolean", "default": false, "markdownDescription": "点击打开 VSCode 快捷键设置页面进行自定义配置", "order": 4}, "代码助手.自定义指令": {"type": "string", "maxLength": 1500, "description": "需要代码助手了解哪些信息，或如何进行回答。例如：我是一名软件开发者，当我问安全问题时，如果回复内容包含代码，请解释这些代码；但如果仅需要提供功能代码时，只提供代码。", "order": 5}}}]}, "scripts": {"vsce:package": "node scripts/switch-assets.js && vsce package", "vsce:package:prod": "cross-env NODE_ENV=production node scripts/switch-assets.js && vsce package", "vscode:prepublish": "yarn run clean:dist && yarn run package", "compile": "yarn run clean:dist && node scripts/switch-assets.js && webpack && yarn run build-codechat", "package": "node scripts/switch-assets.js && webpack --mode production --devtool hidden-source-map && yarn run build-codechat", "watch": "yarn run clean:dist && node scripts/switch-assets.js && webpack && yarn run watch-codechat", "watch-vscode": "yarn run clean:dist && node scripts/switch-assets.js && yarn run build-codechat && webpack --watch", "watch-vscode-prod": "yarn run clean:dist && node scripts/switch-assets.js && yarn run build-codechat && cross-env NODE_ENV=production webpack --watch", "watch-codechat": "cd webview/codechat && yarn run build-watch", "build-codechat": "cd webview/codechat && yarn run build", "clean:dist": "<PERSON><PERSON><PERSON> dist", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "yarn run compile-tests && yarn run compile && yarn run lint", "lint": "eslint ./src --ext ts --fix", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/glob": "^8.1.0", "@types/mocha": "^10.0.1", "@types/node": "20.2.5", "@types/proper-lockfile": "^4.1.4", "@types/tmp": "^0.2.3", "@types/vscode": "^1.68.1", "@types/ws": "^8.5.5", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8", "@vscode/test-electron": "^2.3.2", "cross-env": "^7.0.3", "dotenv-webpack": "^8.0.1", "eslint": "^8.41.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "glob": "^8.1.0", "mocha": "^10.2.0", "prettier": "^2.8.3", "rimraf": "^5.0.5", "ts-loader": "^9.4.3", "typescript": "^5.1.3", "vsce": "^2.15.0", "webpack": "^5.85.0", "webpack-cli": "^5.1.1"}, "dependencies": {"@ai-zen/node-fetch-event-source": "2.1.0", "@types/archiver": "^6.0.2", "@types/diff": "^7.0.2", "@types/markdown-it-attrs": "^4.1.3", "abort-controller": "^3.0.0", "archiver": "^7.0.1", "axios": "^1.8.4", "diff": "^7.0.0", "dotenv": "^8.6.0", "form-data": "^4.0.0", "fs-extra": "^11.3.0", "fuse.js": "6.4.6", "http-proxy-agent": "^7.0.0", "macaddress": "^0.5.3", "markdown-it-attrs": "^4.1.6", "node-fetch": "^3.3.2", "node-machine-id": "^1.1.12", "proper-lockfile": "^4.1.2", "rxjs": "^7.8.2", "uuid": "^11.1.0", "web-tree-sitter": "^0.23.0", "ws": "^8.14.0", "zip-lib": "^1.0.5"}, "optionalDependencies": {"bufferutil": "^4.0.7", "utf-8-validate": "^6.0.3"}, "workspaces": ["webview/*"], "__metadata": {"publisherDisplayName": "海云安"}}