import { Command, Disposable, StatusBarAlignment, StatusBarItem, ThemeColor, window } from 'vscode';
import { APP_NAME, RtnCode, RtnMessage, SrdCommand } from '../common/constants';
import { StatusBarState } from './types';

export default class SrdStatusBarItem implements Disposable {
  private item: StatusBarItem;

  private showAppName = false;

  public constructor(showAppName?: boolean) {
    if (showAppName !== undefined) {
      this.showAppName = showAppName;
    }

    this.item = window.createStatusBarItem(StatusBarAlignment.Left, -1);
    this.setDefault();
    this.item.show();
  }

  public dispose() {
    this.item.dispose();
    if (this.settingsItem) {
      this.settingsItem.dispose();
      this.settingsItem = undefined;
    }
  }

  public setStatusView(state: StatusBarState, code?: number) {
    switch (state) {
      case StatusBarState.NotLogin:
        this.setDefault('未登录', '点击进行登录', 'srd-copilot-unlogin');
        this.setCommand(SrdCommand.LOGIN);
        break;
      case StatusBarState.Logining:
        this.setLoading('登录中...', '正在登录');
        this.setCommand(undefined);
        break;
      case StatusBarState.CodeCompleteEnabled:
        this.setDefault('自动补全启用', '点击可禁用', 'srd-copilot-code-enabled');
        this.setCommand(SrdCommand.TOGGLE_CODECOMPLETE);
        break;
      case StatusBarState.CodeCompleteDisabled:
        this.setWarning('自动补全禁用', '点击可启用', 'srd-copilot-code-disabled');
        this.setCommand(SrdCommand.TOGGLE_CODECOMPLETE);
        break;
      case StatusBarState.WaitingAutoComplete:
        this.setLoading('获取自动补全代码中...', '正在获取自动补全代码');
        this.setCommand(undefined);
        break;
      case StatusBarState.WaitingManualComplete:
        this.setLoading('获取手动补全代码中...', '正在获取手动补全代码');
        this.setCommand(undefined);
        break;
      case StatusBarState.WSServerError: {
        code = code || RtnCode.NO_CHANNEL;
        const msg = RtnMessage[code];
        this.setError(
          msg,
          msg,
          code === RtnCode.NO_CHANNEL ? 'srd-copilot-unconnect' : 'srd-copilot-error-info'
        );
        this.setCommand(
          code === RtnCode.INVALID_USER || code === RtnCode.USER_FORBIDDEN
            ? {
                command: SrdCommand.SHOW_SVR_ERROR,
                arguments: [code],
                title: '',
              }
            : undefined
        );
        break;
      }
      case StatusBarState.SyncingAgent:
        this.setLoading('同步服务中...', '正在下载和安装智能编程服务');
        this.setCommand(undefined);
        break;
      case StatusBarState.AgentSyncFailed:
        this.setError('同步服务失败', '下载智能服务失败', 'error');
        this.setCommand(undefined);
        break;
      case StatusBarState.AgentStartupFailed:
        this.setError('服务启动失败', '启动智能服务失败', 'error');
        this.setCommand(undefined);
        break;
      case StatusBarState.CodeIndexing:
        const progress = code !== undefined ? Math.round(code) : 0;
        this.setLoading(`正在更新索引 (${progress}%)`, '正在更新代码索引，这可能需要一些时间');
        this.setCommand(undefined);
        break;
      default:
        break;
    }
  }
  public setStatusViewSec(state: StatusBarState, code?: number) {
    // 保存当前状态以便设置命令时使用
    this.currentState = state;
    this.currentCode = code;
    
    switch (state) {
      case StatusBarState.NotLogin:
        this.setDefault('未登录', '点击进行登录', 'srd-copilot-unlogin');
        this.setCommand(SrdCommand.LOGIN);
        break;
      case StatusBarState.Logining:
        this.setLoading('登录中...', '正在登录');
        this.setCommand(undefined);
        break;
      case StatusBarState.CodeCompleteEnabled:
        this.setDefault('自动补全启用', '点击可禁用', 'srd-copilot-code-enabled');
        this.setCommand(SrdCommand.TOGGLE_CODECOMPLETE);
        break;
      case StatusBarState.CodeCompleteDisabled:
        this.setWarning('自动补全禁用', '点击可启用', 'srd-copilot-code-disabled');
        this.setCommand(SrdCommand.TOGGLE_CODECOMPLETE);
        break;
      case StatusBarState.WaitingAutoComplete:
        this.setLoading('获取自动补全代码中...', '正在获取自动补全代码');
        this.setCommand(undefined);
        break;
      case StatusBarState.WaitingManualComplete:
        this.setLoading('获取手动补全代码中...', '正在获取手动补全代码');
        this.setCommand(undefined);
        break;
      case StatusBarState.WSServerError: {
        code = code || RtnCode.NO_CHANNEL;
        const msg = RtnMessage[code];
        this.setError(
          msg,
          msg,
          code === RtnCode.NO_CHANNEL ? 'srd-copilot-unconnect' : 'srd-copilot-error-info'
        );
        this.setCommand(
          code === RtnCode.INVALID_USER || code === RtnCode.USER_FORBIDDEN
            ? {
              command: SrdCommand.SHOW_SVR_ERROR,
              arguments: [code],
              title: '',
            }
            : undefined
        );
        break;
      }
      case StatusBarState.SyncingAgent:
        this.setLoading('同步服务中...', '正在下载和安装智能编程服务');
        this.setCommand(undefined);
        break;
      case StatusBarState.AgentSyncFailed:
        this.setError('同步服务失败', '下载智能服务失败', 'error');
        this.setCommand(undefined);
        break;
      case StatusBarState.AgentStartupFailed:
        this.setError('服务启动失败', '启动智能服务失败', 'error');
        this.setCommand(undefined);
        break;
      case StatusBarState.CodeIndexing:
        const progress = code !== undefined ? Math.round(code) : 0;
        this.setLoading(`正在更新索引 (${progress}%)`, '正在更新代码索引，这可能需要一些时间');
        this.setCommand(undefined);
        break;
      default:
        break;
    }
    
    // 添加设置按钮
    this.addSettingsButton();
  }

  // 存储当前状态
  private currentState: StatusBarState | undefined;
  private currentCode: number | undefined;

  // 添加设置按钮
  private addSettingsButton() {
    // 创建设置按钮的状态栏项
    if (!this.settingsItem) {
      this.settingsItem = window.createStatusBarItem(StatusBarAlignment.Left, -0.5);
      this.settingsItem.text = '$(gear)';
      this.settingsItem.tooltip = '打开海云安代码助手设置';
      this.settingsItem.command = SrdCommand.SETTINGS;
      this.settingsItem.show();
    }
  }

  // 设置按钮状态栏项
  private settingsItem: StatusBarItem | undefined;

  private setDefault(statusText?: string, message?: string, icon?: string) {
    this.item.backgroundColor = undefined;

    if (statusText) {
      this.setText(statusText, icon);
    } else {
      this.item.text = '';
    }

    if (message) {
      this.item.tooltip = message;
    }
  }

  private setLoading(statusText: string, message: string) {
    this.setText(statusText, 'loading~spin');
    this.item.backgroundColor = undefined;
    this.item.tooltip = message;
  }

  private setError(statusText: string, message: string, icon?: string) {
    this.setText(statusText, icon);
    this.item.backgroundColor = new ThemeColor('statusBarItem.errorBackground');
    this.item.tooltip = message;
  }

  private setWarning(statusText: string, message: string, icon?: string) {
    this.setText(statusText, icon);
    this.item.backgroundColor = new ThemeColor('statusBarItem.warningBackground');
    this.item.tooltip = message;
  }

  private setCommand(command: string | Command | undefined) {
    this.item.command = command;
  }

  private setText(text: string, icon?: string) {
    const baseText = icon ? `$(${icon}) ${text}` : text;

    if (this.showAppName) {
      this.item.text = `${APP_NAME} (${baseText})`;
    } else {
      this.item.text = baseText;
    }
  }
}
