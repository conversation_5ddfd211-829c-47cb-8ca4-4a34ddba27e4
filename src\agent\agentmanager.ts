import * as path from 'path';
import { SystemUtils } from './utils/system';
import { AgentDownloader } from './services/downloader';
import { VersionChecker } from './services/versionChecker';
import { ProcessManager } from './services/processManager';
import { AgentConfig, CORE_AGENT, TABBY_AGENT } from './types';
import { AgentCommClient } from './commclient/agentcommclient';
import { IAgentMessageReceiver } from './commclient/MessageReceiver';
import { TabbyAgentProcess } from './process/TabbyAgentProcess';
import { CoreAgentProcess } from './process/CoreAgentProcess';
import { Logger } from '../utils/logger';
import { TabbyAgentClient } from './commclient/tabbyAgentClient';
import { CoreAgentClient } from './commclient/coreAgentClient';
import { CoreAgentMessageReceiver } from './commclient/coreAgentReceiver';
import { TabbyAgentMessageReceiver } from './commclient/tabbyAgentReceiver';
import { FileLockUtil } from './utils/lockUtil';
import { IStatusBarHandler } from '../statusbar/types';
import { AgentStatus } from '../common/constants';
import * as vscode from 'vscode';
import { AgentProcess } from './process/AgentProcess';

export class AgentManager {
  private config: Map<string, AgentConfig> = new Map<string, AgentConfig>();

  private downloader!: AgentDownloader;

  private versionChecker!: VersionChecker;

  private processManager!: ProcessManager;

  private pluginName!: string;

  private agentCommClients: Map<string, AgentCommClient> = new Map();

  private messageReceivers!: Map<string, IAgentMessageReceiver>;

  private statusBarHandler: IStatusBarHandler | null = null;
  private agentProcessMap: Map<string, AgentProcess> = new Map();
  

  // 单例模式
  private static instance: AgentManager | null;

  public constructor() { }

  public static getInstance(): AgentManager {
    if (AgentManager.instance == null) {
      AgentManager.instance = new AgentManager();
    }
    return AgentManager.instance;
  }

  public init(
    config: Map<string, AgentConfig>,
    pluginName: string,
    messageReceivers: Map<string, IAgentMessageReceiver>
  ) {
    this.config = config;
    this.pluginName = pluginName;
    this.messageReceivers = messageReceivers;
    this.downloader = new AgentDownloader();
    this.versionChecker = new VersionChecker();
    this.processManager = new ProcessManager(config, this); // Pass AgentManager instance
  }

  public async initialize(): Promise<void> {
    //启动需要的agent进程
    const agentProcessMap = new Map([
      ['core', new CoreAgentProcess()],
      ['tabby', new TabbyAgentProcess()]
    ]);
    this.agentProcessMap = agentProcessMap;
    for (const [agentName, agent] of agentProcessMap) {
      const agentPath = SystemUtils.getAgentPath(this.pluginName, agent.getAgentName());

      try {
        // 启动agent进程
        const process = await this.processManager.startAgent(agent, agentPath, 0);

        // 创建并存储AgentCommClient实例
        const messageReceiver = this.messageReceivers.get(agent.getAgentName());
        if (!messageReceiver) {
          throw new Error(`No message receiver found for agent: ${agent.getAgentName()}`);
        }
        if (agentName === TABBY_AGENT) {
          const commClient = new TabbyAgentClient(process, messageReceiver);
          this.agentCommClients.set(agentName, commClient);
          continue;
        } 
        const commClient = new AgentCommClient(process, messageReceiver);
        this.agentCommClients.set(agent.getAgentName(), commClient);
      } catch (error) {
        // 处理启动失败情况
        Logger.error(`[AgentManager] 启动${agentName}进程失败: ${error}`);
        
        // 通知状态栏启动失败
        if (this.statusBarHandler) {
          let errorMessage = '服务启动失败';
          if (error instanceof Error) {
            errorMessage = error.message;
          } else if (typeof error === 'string') {
            errorMessage = error;
          } else if (error && typeof error === 'object' && 'message' in error) {
            errorMessage = String(error.message);
          }
          this.statusBarHandler.onAgentStatusChange(AgentStatus.STARTUP_FAILED, errorMessage);
        }
        
        // 继续抛出异常或者根据需要决定是否继续
        throw error;
      }
    }
  }

  public onAgentRestarted(agentName: string, newProcess: any): void {
    // Handle agent restart notification from ProcessManager
    const existingClient = this.agentCommClients.get(agentName);
    if (existingClient) {
      existingClient.shutdown();
      this.agentCommClients.delete(agentName);
    }

    const messageReceiver = this.messageReceivers.get(agentName);
    if (messageReceiver) {
      if (agentName === TABBY_AGENT) {
        const newCommClient = new TabbyAgentClient(newProcess, messageReceiver);
        this.agentCommClients.set(agentName, newCommClient);
      } else {
        const newCommClient = new CoreAgentClient(newProcess, messageReceiver);
        this.agentCommClients.set(agentName, newCommClient);
      }
    }
  }

  public shutdown(): void {
    // Shutdown all comm clients
    for (const client of this.agentCommClients.values()) {
      client.shutdown();
    }
    this.agentCommClients.clear();

    this.processManager.stopAll();
  }

  /**
   * 根据agent名称获取对应的AgentCommClient
   * @param agentName agent名称
   * @returns 对应的AgentCommClient实例，如果不存在则返回null
   */
  public getAgentCommClient(agentName: string): AgentCommClient | null {
    const client = this.agentCommClients.get(agentName);
    if (!client) {
      return null;
    }
    return client;
  }

  /**
   * 注册一个状态栏处理程序以接收代理状态更新
   * @param handler 状态栏处理程序
   */
  public registerStatusBarHandler(handler: IStatusBarHandler): void {
    this.statusBarHandler = handler;
  }

  /**
   * 检查并下载Agent
   * @returns 下载成功返回true，任何一个Agent下载失败则返回false
   */
  public async checkAndDownloadAgents(): Promise<boolean> {
    try {
      // 获取配置的Agent版本信息
      const agentVersions = this.versionChecker.getConfiguredAgentVersions();

      if (agentVersions.length === 0) {
        Logger.warn('[AgentManager] 未找到配置的Agent版本信息，跳过下载');
        return false; // 没有从远端拿到下载的Agent版本，视为失败
      }

      // 获取配置文件路径，这里现在jetbrain和vscode的配置文件路径是一样的
      const configPath = SystemUtils.getAgentConfigPath(this.pluginName);

      // 获取锁文件路径
      const lockFilePath = SystemUtils.getAgentLockFilePath(this.pluginName);

      let allDownloadsSuccessful = true; // 标记是否所有下载都成功
      
      try {
        // 使用文件锁执行检查和下载过程
        await FileLockUtil.execute(lockFilePath, async () => {
          // 读取本地配置
          const localConfig = await this.versionChecker.readLocalVersions(configPath);
          const osType = SystemUtils.getLocalConfig();

          // 检查哪些Agent需要更新
          const agentsToUpdate = [];

          for (const { agentName, version, downloadUrl, md5 } of agentVersions) {
            // 获取本地版本
            const localVersions = localConfig.agent[agentName]?.[osType];
            
            // 检查远端版本是否存在于本地版本列表中
            const hasVersion = localVersions && localVersions.some(v => v.version === version);
            
            // 判断是否需要更新：当远端版本不在本地版本列表中时需要更新
            const needsUpdate = !hasVersion;
            Logger.info(`agent: ${agentName}, 远端版本号: ${version}, 本地是否已有该版本: ${hasVersion}, 需要更新: ${needsUpdate}`);

            if (needsUpdate && downloadUrl) {
                agentsToUpdate.push({ agentName, version, downloadUrl, md5 });
            }
          }

          if (agentsToUpdate.length === 0) {
            return;
          }
          
          // 通知状态栏正在同步开始
          if (this.statusBarHandler) {
            this.statusBarHandler.onAgentStatusChange(AgentStatus.SYNCING);
          }
          
          // 创建一个下载任务的数组
          const downloadTasks = agentsToUpdate.map(async ({ agentName, version, downloadUrl, md5 }) => {
            const targetPath = SystemUtils.getAgentDownloadPath(this.pluginName, agentName);

            try {
              const success = await this.downloader.downloadAgent(
                downloadUrl,
                agentName,
                targetPath,
                md5
              );

              if (success) {
                return { agentName, version, downloadUrl, md5, success: true };
              } else {
                Logger.error(`[AgentManager] ${agentName} 下载失败`);
                return { agentName, success: false };
              }
            } catch (error) {
              Logger.error(`[AgentManager] 下载 ${agentName} 时出错: ${error}`);
              return { agentName, success: false, error };
            }
          });

          // 等待所有下载任务完成
          const results = await Promise.all(downloadTasks);
          
          // 检查是否所有下载都成功
          allDownloadsSuccessful = results.every(result => result.success);
          
          if (!allDownloadsSuccessful) {
            Logger.warn('[AgentManager] 部分Agent下载失败，请检查日志');
            
            // 任何下载失败都显示错误弹窗和更新状态栏
            if (this.statusBarHandler) {
              this.statusBarHandler.onAgentStatusChange(AgentStatus.SYNC_FAILED, '未知错误');
            }
            
            // 显示错误信息，包含失败数量
            vscode.window.showErrorMessage('服务下载失败，请重启');
            return; // 提前返回，但不影响allDownloadsSuccessful的值
          }
          
          // 收集所有成功下载的Agent
          const successfulUpdates = results
            .filter(result => result.success && result.version && result.downloadUrl && result.md5)
            .map(({ agentName, version, downloadUrl, md5 }) => ({ 
              agentName, 
              version: version as string, 
              downloadUrl: downloadUrl as string, 
              md5: md5 as string 
            }));

          // 统一更新所有成功下载的Agent版本记录
          if (successfulUpdates.length > 0) {
            await this.versionChecker.updateLocalVersions(configPath, successfulUpdates);
          }
        });
      } finally {
        // 通知状态栏同步完成
        if (this.statusBarHandler && allDownloadsSuccessful) {
          this.statusBarHandler.onAgentStatusChange(AgentStatus.SYNC_COMPLETED);
        }
      }
      
      return allDownloadsSuccessful;
    } catch (error) {
      Logger.error(`[AgentManager] 检查和下载Agent时出错: ${error}`);
      // 处理错误
      if (this.statusBarHandler) {
        // 类型安全地处理错误消息
        let errorMessage = '未知错误';
        if (error instanceof Error) {
          errorMessage = error.message;
        } else if (typeof error === 'string') {
          errorMessage = error;
        } else if (error && typeof error === 'object' && 'message' in error) {
          errorMessage = String(error.message);
        }
        this.statusBarHandler.onAgentStatusChange(AgentStatus.SYNC_FAILED, errorMessage);
        vscode.window.showErrorMessage('服务下载失败，请重启');
      }
      return false; // 发生异常也返回失败
    }
  }

  /**
   * 更新索引进度状态
   * @param progress 索引进度（0-100）
   */
  public updateIndexingProgress(progress: number): void {
    if (this.statusBarHandler) {
      this.statusBarHandler.onAgentStatusChange(AgentStatus.INDEXING, progress);
    }
  }

  /**
   * 处理索引完成
   */
  public onIndexingComplete(): void {
    if (this.statusBarHandler) {
      this.statusBarHandler.onAgentStatusChange(AgentStatus.SYNC_COMPLETED);
    }
  }

  public isAgentRunning(agentName: string) { 
    if (this.agentProcessMap.has(agentName)) { 
      const agent = this.agentProcessMap.get(agentName);
      return agent!.checkProcessHealth();
    }
    return false;
  }
}
