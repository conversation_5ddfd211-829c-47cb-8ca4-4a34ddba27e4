import { ChildProcess } from "child_process";
import { AgentProcess } from "./AgentProcess";
import { spawn } from 'child_process';
import { AgentConfig, TABBY_AGENT } from "../types";
import { SystemUtils } from '../utils/system';
import { Logger } from "../../utils/logger";
import { getEnvTag } from "../../utils/common";
import * as path from 'path';
import { generateIgnoreList } from "../../utils/common";

export class TabbyAgentProcess extends AgentProcess {
  public constructor() {
    super()
  }

  public buildProcess(agentPath: string, port: number, config: AgentConfig): ChildProcess {
    const baseBrand = process.env.ISSEC !== 'false' ? 'oscap' : 'codefree';
    const nodePath = SystemUtils.getAgentPath(baseBrand, 'node');
    const basePath = SystemUtils.getAgentBasePath(baseBrand)
    SystemUtils.setPermission(nodePath);
    SystemUtils.removeQuarantine(basePath);

    const childProcess = spawn(nodePath, [agentPath], {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        LANG: 'en_US.UTF-8',
        LC_ALL: 'en_US.UTF-8',
        NODE_SKIP_PLATFORM_CHECK: '1',
        "apiKey": config.apiKey,
        "invokerId": config.invokerId,
        "pluginType": config.pluginType,
        "pluginVersion": config.pluginVersion,
        "clientType": config.clientType,
        "clientVersion": config.clientVersion,
        "serverType": config.serverType,
        "serverBaseUrl": config.serverBaseUrl,
        "cplSubservice": config.cplSubservice,
        "env": getEnvTag(),
        // todo:ignore list
        'ignoreList': generateIgnoreList(),
      },
      detached: false,
      cwd: path.dirname(agentPath),
    });

    this.process = childProcess;

    return childProcess
  }


  public getAgentName(): string {
    return TABBY_AGENT
  }
}